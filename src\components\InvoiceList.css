/**
 * Styling untuk InvoiceList component
 * Konsisten dengan design system PWA
 */

/* Professional gradient background */
.professional-gradient-bg {
  background: linear-gradient(135deg, 
    oklch(var(--p) / 0.05) 0%, 
    oklch(var(--s) / 0.03) 25%, 
    oklch(var(--a) / 0.02) 50%, 
    oklch(var(--p) / 0.03) 75%, 
    oklch(var(--s) / 0.05) 100%
  );
  min-height: 100vh;
  font-family: 'Poppins', sans-serif;
}

/* Table styling yang konsisten dengan theme */
.table {
  border-radius: 0.75rem;
  overflow: hidden;
  background-color: var(--fallback-b1, oklch(var(--b1)));
}

.table th {
  background-color: var(--fallback-p, oklch(var(--p) / 0.1));
  color: var(--fallback-p, oklch(var(--p)));
  font-weight: 600;
  font-size: 0.875rem;
  padding: 1rem 0.75rem;
  border-bottom: 2px solid var(--fallback-p, oklch(var(--p) / 0.2));
}

.table td {
  padding: 0.875rem 0.75rem;
  font-size: 0.875rem;
  vertical-align: middle;
}

.table tbody tr:hover {
  background-color: var(--fallback-b2, oklch(var(--b2) / 0.5));
  transition: background-color 0.2s ease;
}

/* Badge styling untuk status */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

.badge-info {
  background-color: var(--fallback-info, oklch(var(--in)));
  color: var(--fallback-info-content, oklch(var(--inc)));
}

.badge-success {
  background-color: var(--fallback-success, oklch(var(--su)));
  color: var(--fallback-success-content, oklch(var(--suc)));
}

.badge-error {
  background-color: var(--fallback-error, oklch(var(--er)));
  color: var(--fallback-error-content, oklch(var(--erc)));
}

.badge-neutral {
  background-color: var(--fallback-neutral, oklch(var(--n)));
  color: var(--fallback-neutral-content, oklch(var(--nc)));
}

/* Card styling */
.card {
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--fallback-b3, oklch(var(--b3)));
}

/* Input styling */
.input {
  border-radius: 0.5rem;
  border: 1px solid var(--fallback-b3, oklch(var(--b3)));
  transition: all 0.2s ease;
}

.input:focus {
  border-color: var(--fallback-p, oklch(var(--p)));
  box-shadow: 0 0 0 3px var(--fallback-p, oklch(var(--p) / 0.1));
}

/* Select styling */
.select {
  border-radius: 0.5rem;
  border: 1px solid var(--fallback-b3, oklch(var(--b3)));
}

/* Button styling */
.btn {
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-outline {
  border: 1px solid var(--fallback-b3, oklch(var(--b3)));
}

.btn-outline:hover {
  background-color: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  border-color: var(--fallback-p, oklch(var(--p)));
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .table th,
  .table td {
    padding: 0.5rem 0.375rem;
    font-size: 0.75rem;
  }
  
  .table th {
    font-size: 0.75rem;
  }
  
  /* Hide less important columns on mobile */
  .table th:nth-child(n+6),
  .table td:nth-child(n+6) {
    display: none;
  }
}

@media (max-width: 640px) {
  /* Hide even more columns on very small screens */
  .table th:nth-child(n+5),
  .table td:nth-child(n+5) {
    display: none;
  }
}

/* Animation for table rows */
.table tbody tr {
  transition: all 0.2s ease;
}

/* Pagination styling */
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-top: 1px solid var(--fallback-b3, oklch(var(--b3)));
  background-color: var(--fallback-b1, oklch(var(--b1)));
}

/* Search container styling */
.search-container {
  background-color: var(--fallback-b1, oklch(var(--b1)));
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Loading state */
.loading-skeleton {
  background: linear-gradient(90deg, 
    var(--fallback-b2, oklch(var(--b2))) 25%, 
    var(--fallback-b3, oklch(var(--b3))) 50%, 
    var(--fallback-b2, oklch(var(--b2))) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}
