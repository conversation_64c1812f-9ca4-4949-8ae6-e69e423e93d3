import React, { useState, useMemo } from 'react';
import UniversalHeader from './UniversalHeader';
import HomeButton from './HomeButton';
import { fullPurchaseOrderData } from '../data/mockData';
import type { PurchaseOrderListProps } from '../types';
import './PurchaseOrderList.css';


/**
 * Komponen PurchaseOrderList - Menampilkan daftar purchase order dalam format datatable
 * Dengan fitur search, pagination, dan styling konsisten dengan PWA
 */
const PurchaseOrderList: React.FC<PurchaseOrderListProps> = ({ className = '' }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter data berdasarkan search query
  const filteredData = useMemo(() => {
    if (!searchQuery.trim()) return fullPurchaseOrderData;
    
    const query = searchQuery.toLowerCase();
    return fullPurchaseOrderData.filter(item => 
      item.mrNumber.toLowerCase().includes(query) ||
      item.unit.toLowerCase().includes(query) ||
      item.status.toLowerCase().includes(query) ||
      item.total.toLowerCase().includes(query)
    );
  }, [searchQuery]);

  // Pagination logic
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Ready WO':
        return 'badge badge-info';
      case 'Diajukan':
        return 'badge badge-warning';
      case 'Approved':
        return 'badge badge-success';
      case 'Rejected':
        return 'badge badge-error';
      default:
        return 'badge badge-neutral';
    }
  };

  return (
    <div className={`min-h-screen professional-gradient-bg ${className}`}>
      {/* Header */}
      <UniversalHeader />

      {/* Page Title Section */}
      <div className="w-full max-w-7xl mx-auto px-4 py-2">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-primary mb-1">Dokumen Menunggu PO</h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-4 max-w-7xl mx-auto">
        {/* Search and Controls */}
        <div className="mb-4 flex flex-col sm:flex-row gap-4 items-center justify-between">
          {/* Search Input */}
          <div className="flex-1 max-w-md">
            <div className="form-control">
              <input
                type="text"
                placeholder="Cari MR, unit, atau status..."
                className="input input-bordered w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Items per page selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-base-content/70">Rows per page:</span>
            <select 
              className="select select-bordered select-sm"
              value={itemsPerPage}
              onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>

        {/* Data Table */}
        <div className="card bg-base-100 shadow-lg">
          <div className="card-body p-0">
            <div className="overflow-x-auto">
              <table className="table table-zebra table-sm">
                <thead>
                  <tr className="bg-primary/10">
                    <th className="text-primary font-semibold">NO</th>
                    <th className="text-primary font-semibold">NO. MR</th>
                    <th className="text-primary font-semibold">UNIT</th>
                    <th className="text-primary font-semibold">STATUS</th>
                    <th className="text-primary font-semibold">TANGGAL MR</th>
                    <th className="text-primary font-semibold">TOTAL</th>
                  </tr>
                </thead>
                <tbody>
                  {currentData.map((item) => (
                    <tr key={item.id} className="hover">
                      <td className="font-medium">{item.no}</td>
                      <td className="font-medium">{item.mrNumber}</td>
                      <td className="font-medium">{item.unit}</td>
                      <td>
                        <span className={getStatusBadgeClass(item.status)}>
                          {item.status}
                        </span>
                      </td>
                      <td>{item.tanggalMr}</td>
                      <td className="font-semibold text-error">{item.total}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="flex flex-col sm:flex-row items-center justify-between p-4 border-t">
              <div className="text-sm text-base-content/70 mb-2 sm:mb-0">
                {startIndex + 1}-{Math.min(endIndex, filteredData.length)} of {filteredData.length}
              </div>
              
              <div className="flex items-center gap-2">
                <button
                  className="btn btn-sm btn-outline"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  ‹
                </button>
                
                <span className="text-sm px-2">
                  {currentPage}/{totalPages}
                </span>
                
                <button
                  className="btn btn-sm btn-outline"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  ›
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Info */}
        <div className="mt-4 text-center text-sm text-base-content/70">
          Menampilkan {filteredData.length} dari {fullPurchaseOrderData.length} total dokumen PO
        </div>
      </div>

      {/* Home Button */}
      <HomeButton />
    </div>
  );
};

export default PurchaseOrderList;
