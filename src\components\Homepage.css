/* Homepage Component Styles - Responsive untuk tablet 11 inch landscape */

/* Android/Web Font Consistency System */
.homepage-container {
  /* Ensure proper text color contrast for Android visibility */
  color: oklch(var(--bc)) !important;
  /* Enhanced font rendering for Android */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
}

/* Override any white text on white background issues */
.homepage-container * {
  color: inherit;
  /* Ensure consistent font weight rendering */
  font-weight: inherit;
  font-family: inherit;
}

/* Ensure proper contrast for all text elements */
.text-base-content {
  color: oklch(var(--bc)) !important;
  /* Force color inheritance for Android WebView */
  -webkit-text-fill-color: oklch(var(--bc)) !important;
}

.text-base-content\/70 {
  color: oklch(var(--bc) / 0.7) !important;
  -webkit-text-fill-color: oklch(var(--bc) / 0.7) !important;
}

.text-base-content\/80 {
  color: oklch(var(--bc) / 0.8) !important;
  -webkit-text-fill-color: oklch(var(--bc) / 0.8) !important;
}

/* Android-specific font fixes */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  body,
  .homepage-container {
    font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif !important;
  }

  /* Ensure proper font weight rendering on Android */
  .font-semibold {
    font-weight: 600 !important;
  }

  .font-bold {
    font-weight: 700 !important;
  }

  .font-medium {
    font-weight: 500 !important;
  }
}

/* Date/Time display specific styles */
.datetime-display {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  /* Prevent any scrolling in datetime display */
  overflow: hidden;
  max-width: 100%;
  max-height: 100%;
}

.datetime-time {
  font-family: "Courier New", monospace;
  white-space: nowrap;
  overflow: hidden;
}

.datetime-date {
  font-weight: 500;
  line-height: 1.4;
  /* Prevent text wrapping issues */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Left section specific no-scroll styling */
.lg\:grid-cols-2 > div:first-child,
.h-2\/5 {
  /* Absolutely prevent any scrolling */
  overflow: hidden !important;
  /* Ensure content fits within bounds */
  box-sizing: border-box;
}

/* Mitra Bisnis (Business Partner) Scrolling Animation */
.partner-scroll-container {
  height: 200px; /* tinggi area scroll */
  overflow: hidden;
  position: relative;

  /* Fade di atas & bawah */
  -webkit-mask-image: linear-gradient(to bottom, transparent, black 20%, black 50%, transparent);
  mask-image: linear-gradient(to bottom, transparent, black 20%, black 50%, transparent);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;

  /* Blur tepi untuk transisi lebih halus */
  backdrop-filter: blur(2px);
}

/* Elemen list di dalam tetap scroll */
.partner-scroll-list {
  overflow-y: auto;
  height: 100%;
  padding-right: 8px; /* beri ruang supaya scrollbar tidak nutup konten */
}

@keyframes scrollPartners {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-25%); /* Scroll up by quarter of total height (since list is quadrupled) */
  }
}

.partner-scroll-list {
  display: flex;
  flex-direction: column;
  gap: 0.6rem; /* Same as space-y-2 */
  animation: scrollPartners 30s linear infinite; /* Slower for smoother effect */
}

.partner-scroll-list:hover {
  animation-play-state: paused;
}

/* Business info cards styling */
.business-info-card {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.business-info-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.95);
}

/* Statistics cards styling */
.stats-card {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Clickable stats cards */
.stats-card.cursor-pointer:hover {
  transform: scale(1.08);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 0.95) !important;
}

/* Gradient Divider - NEW */
.gradient-divider {
  height: 2px;
  border: 0;
  background-image: linear-gradient(to right, transparent, oklch(var(--p) / 0.5), transparent);
}

/* Consistent Gradient Background System */
.gradient-bg-primary {
  background: linear-gradient(135deg, oklch(var(--p) / 0.15) 0%, oklch(var(--s) / 0.1) 50%, oklch(var(--a) / 0.05) 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(45deg, oklch(var(--s) / 0.1) 0%, oklch(var(--p) / 0.08) 50%, oklch(var(--a) / 0.12) 100%);
}

.gradient-bg-accent {
  background: linear-gradient(225deg, oklch(var(--a) / 0.1) 0%, oklch(var(--p) / 0.05) 50%, oklch(var(--s) / 0.08) 100%);
}

/* Enhanced gradient for main backgrounds */
.bg-gradient-to-br {
  position: relative;
  background: linear-gradient(135deg, oklch(var(--p) / 0.12) 0%, oklch(var(--s) / 0.08) 35%, oklch(var(--a) / 0.06) 70%, oklch(var(--p) / 0.04) 100%);
}

.bg-gradient-to-br::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, oklch(var(--p) / 0.08) 0%, transparent 50%), radial-gradient(circle at 70% 80%, oklch(var(--s) / 0.06) 0%, transparent 50%);
  z-index: 0;
}

.bg-gradient-to-br > * {
  position: relative;
  z-index: 1;
}

/* Professional gradient background for main container */
.professional-gradient-bg {
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 15%, #cbd5e1 35%, #94a3b8 60%, #64748b 85%, #475569 100%);

  /* Add subtle overlay for depth */
  background-image: radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 50%, rgba(241, 245, 249, 0.5) 100%);

  /* Ensure proper contrast for Android PWA */
  color: #1e293b;

  /* Add subtle texture for professional look */
  background-attachment: fixed;
}

.professional-gradient-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 40%), radial-gradient(circle at 80% 70%, rgba(147, 51, 234, 0.04) 0%, transparent 40%),
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  z-index: 0;
  pointer-events: none;
}

.professional-gradient-bg > * {
  position: relative;
  z-index: 1;
}

/* CSS Variables untuk animasi carousel */
:root {
  --item1-transform: translateX(-100%) translateY(-5%) scale(1.5);
  --item1-filter: blur(30px);
  --item1-zIndex: 11;
  --item1-opacity: 0;

  --item2-transform: translateX(0);
  --item2-filter: blur(0px);
  --item2-zIndex: 10;
  --item2-opacity: 1;

  --item3-transform: translate(50%, 10%) scale(0.8);
  --item3-filter: blur(10px);
  --item3-zIndex: 9;
  --item3-opacity: 1;

  --item4-transform: translate(90%, 20%) scale(0.5);
  --item4-filter: blur(30px);
  --item4-zIndex: 8;
  --item4-opacity: 1;

  --item5-transform: translate(120%, 30%) scale(0.3);
  --item5-filter: blur(40px);
  --item5-zIndex: 7;
  --item5-opacity: 0;
}

/* Performance optimizations for animations */
.carousel,
.carousel-item,
.product-image,
.nav-btn,
.see-more-btn,
.back-btn {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

/* Smooth animation reset */
.carousel.next,
.carousel.prev,
.carousel.showDetail {
  animation-fill-mode: forwards;
}

/* Prevent animation conflicts */
.carousel.next .carousel-item,
.carousel.prev .carousel-item {
  animation-fill-mode: forwards;
}

/* Container utama homepage */
.homepage-container {
  width: 100%;
  height: 100%;
  font-family: "Poppins", sans-serif;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header styling */
.header {
  width: 1140px;
  max-width: 90%;
  display: flex;
  justify-content: flex-start;
  margin: auto;
  height: 60px;
  align-items: center;
  padding: 0 20px;
  flex-shrink: 0;
}

.company-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(105, 62, 255, 0.2);
}

.company-name {
  font-weight: 600;
  font-size: 1.4rem;
  color: #693eff;
  letter-spacing: 0.5px;
}

/* Carousel styling */
.carousel {
  position: relative;
  flex: 1;
  overflow: hidden;
  margin-top: -50px;
  min-height: 0;
}

.carousel-list {
  position: absolute;
  width: 1140px;
  max-width: 90%;
  height: 80%;
  left: 50%;
  transform: translateX(-50%);
}

.carousel-item {
  position: absolute;
  left: 0%;
  width: 70%;
  height: 100%;
  font-size: 15px;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity, filter;
}

.carousel-item:nth-child(n + 6) {
  opacity: 0;
}

.carousel-item:nth-child(2) {
  z-index: 10;
  transform: translateX(0);
}

.product-image {
  width: 50%;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: all 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  object-fit: contain;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

.carousel-item:nth-child(2) .product-image:hover {
  transform: translateY(-50%) scale(1.05);
  filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.15));
}

/* Introduce section styling */
.introduce {
  opacity: 0;
  pointer-events: none;
}

.carousel-item:nth-child(2) .introduce {
  opacity: 1;
  pointer-events: auto;
  width: 400px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: opacity 0.5s;
}

.title {
  font-size: 2em;
  font-weight: 500;
  line-height: 1em;
  color: #555;
}

.topic {
  font-size: 4em;
  font-weight: 500;
  color: #333;
  margin: 10px 0;
}

.description {
  font-size: small;
  color: #5559;
  margin: 20px 0;
  line-height: 1.6;
}

.see-more-btn {
  margin-top: 1em;
  padding:4px 8px;
  border: none;
  border-bottom: 2px solid #555;
  background-color: transparent;
  font-weight: bold;
  letter-spacing: 1px;
  transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border-radius: 4px 4px 0 0;
}

.see-more-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(105, 62, 255, 0.1), transparent);
  transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.see-more-btn:hover::before {
  left: 100%;
}

.see-more-btn:hover {
  background: linear-gradient(135deg, rgba(105, 62, 255, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
  padding: 8px 20px;
  transform: translateY(-2px);
  border-bottom-color: #693eff;
  box-shadow: 0 4px 8px rgba(105, 62, 255, 0.2);
}

.see-more-btn:active {
  transform: translateY(-1px);
  transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Detail section styling */
.detail {
  opacity: 0;
  pointer-events: none;
}

.detail-title {
  font-size: 4em;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.detail-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.specifications {
  display: flex;
  gap: 10px;
  width: 100%;
  border-top: 1px solid #5553;
  margin-top: 20px;
  padding-top: 20px;
  flex-wrap: wrap;
}

.spec-item {
  width: 90px;
  text-align: center;
  flex-shrink: 0;
}

.spec-item p:nth-child(1) {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.spec-item p:nth-child(2) {
  color: #666;
  font-size: 0.9rem;
}

.checkout {
  margin-top: 30px;
  display: flex;
  gap: 10px;
}

.checkout button {
  font-family: "Poppins", sans-serif;
  padding: 12px 24px;
  letter-spacing: 2px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.checkout button::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  z-index: 0;
}

.checkout button:hover::before {
  width: 200%;
  height: 200%;
}

.checkout button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.checkout button:active {
  transform: translateY(-1px);
  transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkout button > * {
  position: relative;
  z-index: 1;
}

/* Auto-rotation indicator */
.auto-rotation-indicator {
  position: absolute;
  bottom: 65px; /* Adjusted position to be above centered nav buttons */
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.progress-bar {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, #ffffff57 0%, #e0daf0 50%, #e5d8f2 100%);
  border-radius: 1px;
  animation: autoRotationProgress 10s linear infinite;
  box-shadow: 0 0 2px rgba(105, 62, 255, 0.2);
}

@keyframes autoRotationProgress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

/* Hide progress bar when showing detail */
.carousel.showDetail .auto-rotation-indicator {
  opacity: 0;
  pointer-events: none;
}

/* Positioning untuk item carousel */
.carousel-item:nth-child(1) {
  transform: var(--item1-transform);
  filter: var(--item1-filter);
  z-index: var(--item1-zIndex);
  opacity: var(--item1-opacity);
  pointer-events: none;
}

.carousel-item:nth-child(3) {
  transform: var(--item3-transform);
  filter: var(--item3-filter);
  z-index: var(--item3-zIndex);
}

.carousel-item:nth-child(4) {
  transform: var(--item4-transform);
  filter: var(--item4-filter);
  z-index: var(--item4-zIndex);
}

.carousel-item:nth-child(5) {
  transform: var(--item5-transform);
  filter: var(--item5-filter);
  opacity: var(--item5-opacity);
  pointer-events: none;
}

/* Animasi untuk konten item aktif */
.carousel-item:nth-child(2) .title,
.carousel-item:nth-child(2) .topic,
.carousel-item:nth-child(2) .description,
.carousel-item:nth-child(2) .see-more-btn {
  opacity: 0;
  animation: showContent 0.5s 1s ease-in-out 1 forwards;
}

@keyframes showContent {
  from {
    transform: translateY(-30px);
    filter: blur(10px);
  }
  to {
    transform: translateY(0);
    opacity: 1;
    filter: blur(0px);
  }
}

.carousel-item:nth-child(2) .topic {
  animation-delay: 1.2s;
}

.carousel-item:nth-child(2) .description {
  animation-delay: 1.4s;
}

.carousel-item:nth-child(2) .see-more-btn {
  animation-delay: 1.6s;
}

/* Navigation arrows -- MODIFIED */
.arrows {
  position: absolute;
  bottom: 10px;
  width: 100%; /* Take full width to center content */
  display: flex;
  justify-content: center; /* Center the buttons */
  gap: 20rem; /* Space between buttons */
  left: 50%;
  transform: translateX(-50%);
}

.nav-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-family: monospace;
  border: 1px solid #5555;
  font-size: large;
  background-color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.nav-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, #693eff 0%, #8b5cf6 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  z-index: -1;
}

.nav-btn:hover::before {
  width: 100%;
  height: 100%;
}

.nav-btn:hover {
  color: white;
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #693eff;
}

.nav-btn:active {
  transform: scale(1.05) translateY(-1px);
  transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.nav-btn:disabled:hover {
  transform: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.back-btn {
  position: absolute;
  z-index: 100;
  bottom: 1px;
  left: 25%;
  transform: translateX(-50%);
  border: none;
  border-bottom: 2px solid #555;
  font-weight: bold;
  letter-spacing: 3px;
  background-color: transparent;
  padding: 2px 8px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s, transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 4px 4px 0 0;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.back-btn.visible {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%); /* Base position */
}

.back-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(105, 62, 255, 0.1), transparent);
  transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.back-btn:hover::before {
  left: 100%;
}

.back-btn:hover {
  background: linear-gradient(135deg, rgba(105, 62, 255, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
  padding: 12px 20px;
  transform: translateX(-50%) translateY(-2px);
  border-bottom-color: #693eff;
  box-shadow: 0 4px 8px rgba(105, 62, 255, 0.2);
}

.back-btn:active {
  transform: translateX(-50%) translateY(-1px);
  transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animasi untuk transisi next */
.carousel.next .carousel-item:nth-child(1) {
  animation: transformFromPosition2 0.5s ease-in-out 1 forwards;
}

@keyframes transformFromPosition2 {
  from {
    transform: var(--item2-transform);
    filter: var(--item2-filter);
    opacity: var(--item2-opacity);
  }
}

.carousel.next .carousel-item:nth-child(2) {
  animation: transformFromPosition3 0.7s ease-in-out 1 forwards;
}

@keyframes transformFromPosition3 {
  from {
    transform: var(--item3-transform);
    filter: var(--item3-filter);
    opacity: var(--item3-opacity);
  }
}

.carousel.next .carousel-item:nth-child(3) {
  animation: transformFromPosition4 0.9s ease-in-out 1 forwards;
}

@keyframes transformFromPosition4 {
  from {
    transform: var(--item4-transform);
    filter: var(--item4-filter);
    opacity: var(--item4-opacity);
  }
}

.carousel.next .carousel-item:nth-child(4) {
  animation: transformFromPosition5 1.1s ease-in-out 1 forwards;
}

@keyframes transformFromPosition5 {
  from {
    transform: var(--item5-transform);
    filter: var(--item5-filter);
    opacity: var(--item5-opacity);
  }
}

/* Animasi untuk transisi previous */
.carousel.prev .carousel-item:nth-child(5) {
  animation: transformFromPosition4 0.5s ease-in-out 1 forwards;
}

.carousel.prev .carousel-item:nth-child(4) {
  animation: transformFromPosition3 0.7s ease-in-out 1 forwards;
}

.carousel.prev .carousel-item:nth-child(3) {
  animation: transformFromPosition2 0.9s ease-in-out 1 forwards;
}

.carousel.prev .carousel-item:nth-child(2) {
  animation: transformFromPosition1 1.1s ease-in-out 1 forwards;
}

@keyframes transformFromPosition1 {
  from {
    transform: var(--item1-transform);
    filter: var(--item1-filter);
    opacity: var(--item1-opacity);
  }
}

/* Show detail animations */
.carousel.showDetail .carousel-item:nth-child(3),
.carousel.showDetail .carousel-item:nth-child(4) {
  left: 100%;
  opacity: 0;
  pointer-events: none;
}

.carousel.showDetail .carousel-item:nth-child(2) {
  width: 100%;
}

.carousel.showDetail .carousel-item:nth-child(2) .introduce {
  opacity: 0;
  pointer-events: none;
}

.carousel.showDetail .carousel-item:nth-child(2) .product-image {
  right: 50%;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail {
  opacity: 1;
  width: 50%;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  text-align: right;
  pointer-events: auto;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .detail-title,
.carousel.showDetail .carousel-item:nth-child(2) .detail .detail-description,
.carousel.showDetail .carousel-item:nth-child(2) .detail .specifications,
.carousel.showDetail .carousel-item:nth-child(2) .detail .checkout {
  opacity: 0;
  animation: showContent 0.5s 1s ease-in-out 1 forwards;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .detail-description {
  animation-delay: 1.2s;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .specifications {
  animation-delay: 1.4s;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .checkout {
  animation-delay: 1.6s;
}

.carousel.showDetail .arrows .nav-btn {
  /* Target only next/prev */
  opacity: 0;
  pointer-events: none;
}

/* Responsive Design untuk Tablet 11 inch Landscape (1024px - 1366px) */
@media screen and (min-width: 1024px) {
  /* Ensure proper layout for tablet landscape with visual divider */
  .lg\:grid-cols-2 > div:first-child {
    border-right: 1px solid oklch(var(--b3) / 0.15);
  }

  /* Responsive adjustments for left section content */
  .datetime-time {
    font-size: 6rem !important; /* Larger font size for time */
    font-weight: bold !important; /* Bold */
    letter-spacing: -0.05em; /* Tighter spacing for large font */
  }

  .datetime-date {
    font-size: 1.75rem !important;
  }

  .business-info-card {
    padding: 0.5rem !important;
  }

  .business-info-card .text-sm {
    font-size: 0.75rem !important;
  }

  .stats-card {
    padding: 0.75rem !important;
    min-width: 70px !important;
  }
}

@media screen and (min-width: 1024px) and (max-width: 1366px) {
  .carousel-item {
    width: 80%;
  }

  .carousel-item:nth-child(2) .introduce {
    width: 350px;
  }

  .topic {
    font-size: 3.5em;
  }

  .detail-title {
    font-size: 3.5em;
  }

  .datetime-time {
    font-size: 6.5rem !important;
    letter-spacing: 0.1rem;
  }

  .specifications {
    gap: 8px;
  }

  .spec-item {
    width: 80px;
  }
}

/* Tablet Portrait specific adjustments */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  /* Ensure proper height distribution */
  .h-2\/5 {
    height: 40% !important;
  }

  .h-3\/5 {
    height: 60% !important;
  }

  .arrows {
    justify-content: space-between; /* Revert to space-between on tablet */
    max-width: 90%;
  }

  .back-btn {
    bottom: 0%;
  }

  .auto-rotation-indicator {
    bottom: -5px;
  }

  /* Adjust datetime display for tablet portrait */
  .datetime-time {
    font-size: 6rem !important; /* Larger font size for time */
    font-weight: 700 !important; /* Bold */
  }

  .datetime-date {
    font-size: 1.25rem !important;
  }

  /* Hide some elements on smaller tablet screens */
  .business-info-card:nth-child(n + 4) {
    display: none;
  }

  .carousel-item {
    width: 90%;
  }

  .carousel-item:nth-child(2) .introduce {
    width: 300px;
  }

  .topic {
    font-size: 3em;
  }

  .detail-title {
    font-size: 2.5em;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail .specifications {
    overflow: auto;
  }

  .specifications {
    gap: 6px;
  }

  .spec-item {
    width: 70px;
    font-size: 0.85rem;
  }
}

/* Responsive Design untuk Mobile */
@media screen and (max-width: 767px) {
  .arrows {
    justify-content: space-between; /* Revert to space-between on mobile */
    max-width: 90%;
  }

  .auto-rotation-indicator {
    bottom: -5px;
    width: 150px;
    height: 2px;
  }

  .back-btn {
    bottom: 0%;
  }

  .carousel-item {
    width: 100%;
    font-size: 10px;
  }

  .carousel-list {
    height: 100%;
  }

  .carousel-item:nth-child(2) .introduce {
    width: 50%;
  }

  .product-image {
    width: 40%;
  }

  .topic {
    font-size: 2.5em;
  }

  .title {
    font-size: 1.5em;
  }

  .description {
    height: 100px;
    overflow: auto;
  }

  .detail-title {
    font-size: 2em;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail {
    backdrop-filter: blur(10px);
    font-size: small;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail .detail-description {
    height: 100px;
    overflow: auto;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail .checkout {
    display: flex;
    width: max-content;
    float: right;
  }

  .specifications {
    flex-direction: column;
    gap: 10px;
  }

  .spec-item {
    width: 100%;
    text-align: left;
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
  }

  .header {
    padding: 0 15px;
    height: 60px;
  }

  .company-name {
    font-size: 1.1rem;
  }

  .company-logo {
    width: 35px;
    height: 35px;
  }

  /* Mobile-specific button improvements */
  .nav-btn {
    width: 35px;
    height: 35px;
    font-size: medium;
  }

  .see-more-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    letter-spacing: 2px;
  }

  .back-btn {
    padding: 8px 12px;
    font-size: 0.8rem;
    letter-spacing: 2px;
  }

  .checkout button {
    padding: 8px 16px;
    font-size: 0.8rem;
    letter-spacing: 1px;
  }
}
