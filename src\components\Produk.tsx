import React, { useState, useEffect } from 'react';
import './Produk.css';
import UniversalHeader from './UniversalHeader';
import HomeButton from './HomeButton';

/**
 * Interface untuk data produk dari <PERSON> API
 */
interface Product {
  id: number;
  name: string;
  category: string;
  image: string;
  description: string;
  price: number | null;
  merek: string | null;
  unit_model: string | null;
  warranty: string | null;
  discount: string | null;
  notes: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Interface untuk API response
 */
interface ApiResponse {
  success: boolean;
  data: Product[];
}

/**
 * Props untuk komponen Produk
 */
interface ProdukProps {
  className?: string;
}

/**
 * Komponen Produk dengan layout grid responsif
 * Menampilkan daftar produk dari <PERSON> API
 */
const Produk: React.FC<ProdukProps> = ({ className = '' }) => {
  const [selectedProduct, setSelectedProduct] = useState<number | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // API base URL - adjust this based on your Laravel backend URL
  const API_BASE_URL = 'http://localhost:8000/api/v1';

  // Fetch products from Laravel API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/products`);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data: ApiResponse = await response.json();
        
        if (data.success) {
          setProducts(data.data);
        } else {
          throw new Error('API returned unsuccessful response');
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch products');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  /**
   * Fungsi untuk menangani klik tombol "Lihat Detail"
   * @param productId - ID produk yang dipilih
   */
  const handleViewProductDetail = (productId: number) => {
    setSelectedProduct(selectedProduct === productId ? null : productId);
  };

  /**
   * Fungsi untuk mendapatkan produk yang dipilih
   */
  const getSelectedProduct = () => {
    return products.find(product => product.id === selectedProduct);
  };

  /**
   * Format harga dalam Rupiah
   */
  const formatPrice = (price: number | null) => {
    if (!price) return 'Harga tidak tersedia';
    return `Rp ${price.toLocaleString('id-ID')}`;
  };

  // Loading state
  if (loading) {
    return (
      <div className={`produk-container h-screen max-h-screen overflow-hidden bg-base-100 flex flex-col ${className}`}>
        <UniversalHeader />
        <div className="container mx-auto p-4 flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="loading loading-spinner loading-lg text-primary"></div>
            <p className="mt-4 text-base-content">Memuat produk...</p>
          </div>
        </div>
        <HomeButton />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`produk-container h-screen max-h-screen overflow-hidden bg-base-100 flex flex-col ${className}`}>
        <UniversalHeader />
        <div className="container mx-auto p-4 flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="alert alert-error">
              <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Error: {error}</span>
            </div>
            <button 
              className="btn btn-primary mt-4" 
              onClick={() => window.location.reload()}
            >
              Coba Lagi
            </button>
          </div>
        </div>
        <HomeButton />
      </div>
    );
  }

  return (
    <div className={`produk-container h-screen max-h-screen overflow-hidden bg-base-100 flex flex-col ${className}`}>
      {/* Universal Header */}
      <UniversalHeader />

      {/* Main Content - Flexible height */}
      <div className="container mx-auto p-4 flex-1 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
          {/* Grid Produk */}
          <div className="lg:col-span-2 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-2">
              {products.map((product) => (
                <div
                  key={product.id}
                  className={`product-card card bg-base-100 hover:shadow-xl transition-all duration-300 ${
                    selectedProduct === product.id ? 'ring-2 ring-primary' : ''
                  }`}
                >
                  {/* Gambar Produk */}
                  <figure className="px-2 pt-2">
                    <img src={product.image} alt={product.name} className="rounded-lg w-full h-20 object-cover" loading="lazy" />
                  </figure>

                  {/* Konten Card */}
                  <div className="card-body p-4">
                    <h2 className="card-title text-lg font-bold text-primary">
                      {product.name}
                    </h2>
                    <p className="text-xs text-base-content opacity-70 mb-2">
                      {product.description}
                    </p>
                    <div className="badge badge-secondary badge-sm mb-3">
                      {product.category}
                    </div>

                    {/* Harga */}
                    {product.price && (
                      <div className="text-sm font-semibold text-primary mb-2">
                        {formatPrice(product.price)}
                      </div>
                    )}

                    {/* Tombol Lihat Detail */}
                    <div className="card-actions justify-end">
                      <button
                        className={`btn btn-primary btn-sm ${
                          selectedProduct === product.id ? 'btn-active' : ''
                        }`}
                        onClick={() => handleViewProductDetail(product.id)}
                      >
                        {selectedProduct === product.id ? 'Tutup Detail' : 'Lihat Detail'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Detail Produk */}
          <div className="lg:col-span-1 overflow-hidden">
            <div className={`product-detail-container h-full transition-all duration-500 ${
              selectedProduct ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'
            }`}>
              {selectedProduct && getSelectedProduct() && (
                <div className="card bg-base-100 shadow-lg h-full flex flex-col">
                  <div className="card-body p-4 flex-1 overflow-hidden flex flex-col">
                    {/* Header Detail */}
                    <div className="flex items-center gap-3 mb-4 flex-shrink-0">
                      <img
                        src={getSelectedProduct()!.image}
                        alt={getSelectedProduct()!.name}
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                      <div>
                        <h3 className="text-lg font-bold text-primary">
                          {getSelectedProduct()!.name}
                        </h3>
                        <div className="badge badge-secondary badge-sm">
                          {getSelectedProduct()!.category}
                        </div>
                      </div>
                    </div>

                    {/* Detail Content */}
                    <div className="flex-1 overflow-y-auto">
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-sm mb-1">Deskripsi:</h4>
                          <p className="text-xs text-base-content opacity-80">
                            {getSelectedProduct()!.description}
                          </p>
                        </div>

                        {getSelectedProduct()!.price && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Harga:</h4>
                            <p className="text-lg font-bold text-primary">
                              {formatPrice(getSelectedProduct()!.price)}
                            </p>
                            {getSelectedProduct()!.discount && (
                              <p className="text-xs text-success">
                                {getSelectedProduct()!.discount}
                              </p>
                            )}
                          </div>
                        )}

                        {getSelectedProduct()!.merek && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Merek:</h4>
                            <p className="text-sm">{getSelectedProduct()!.merek}</p>
                          </div>
                        )}

                        {getSelectedProduct()!.unit_model && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Model:</h4>
                            <p className="text-sm">{getSelectedProduct()!.unit_model}</p>
                          </div>
                        )}

                        {getSelectedProduct()!.warranty && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Garansi:</h4>
                            <div className="badge badge-info badge-sm">
                              {getSelectedProduct()!.warranty}
                            </div>
                          </div>
                        )}

                        {getSelectedProduct()!.notes && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Catatan:</h4>
                            <p className="text-xs text-base-content opacity-80">
                              {getSelectedProduct()!.notes}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Home Button */}
      <HomeButton />
    </div>
  );
};

export default Produk;
